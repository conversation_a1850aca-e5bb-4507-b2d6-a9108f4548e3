{"ast": null, "code": "import { gql } from '@apollo/client';\nexport const LOGIN_MUTATION = gql`\n  mutation login ($username: String!, $password: String!) {\n  login(username: $username, password: $password) {\n    message\n    user {\n      id\n      username\n      email\n      token\n    }\n  }\n}\n`;\nexport const SIGNUP_MUTATION = gql`\n  mutation signup($username: String!, $email: String!, $password: String!) {\n    signup(username: $username, email: $email, password: $password) {\n      message\n      user {\n        id\n        username\n        email\n      }\n    }\n  }\n`;\nexport const ADD_TO_CART_MUTATION = gql`\n  mutation AddToCart($productId: ID!, $quantity: Int!, $selectedAttributes: String) {\n    addToCart(productId: $productId, quantity: $quantity, selectedAttributes: $selectedAttributes) {\n      id\n      product {\n        id\n        name\n        price\n        image\n        attributes\n      }\n      quantity\n      selectedAttributes\n    }\n  }\n`;\n\n// Mutation to update cart item quantity\nexport const UPDATE_CART_MUTATION = gql`\n  mutation updateCart($itemId: ID!, $quantityChange: Int!) {\n    updateCart(itemId: $itemId, quantityChange: $quantityChange) {\n      id\n      quantity\n    }\n  }\n`;\n\n// Mutation to remove an item from the cart\nexport const REMOVE_FROM_CART_MUTATION = gql`\n  mutation RemoveFromCart($itemId: ID!) {\n    removeFromCart(itemId: $itemId) {\n      id\n      product {\n        id\n        name\n        price\n        image\n      }\n      quantity\n    }\n  }\n`;\n\n// Mutation to place an order\nexport const PLACE_ORDER_MUTATION = gql`\n  mutation PlaceOrder {\n    placeOrder {\n      success\n      message\n    }\n  }\n`;", "map": {"version": 3, "names": ["gql", "LOGIN_MUTATION", "SIGNUP_MUTATION", "ADD_TO_CART_MUTATION", "UPDATE_CART_MUTATION", "REMOVE_FROM_CART_MUTATION", "PLACE_ORDER_MUTATION"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/graphql/mutations.js"], "sourcesContent": ["import { gql } from '@apollo/client';\r\n\r\nexport const LOGIN_MUTATION = gql`\r\n  mutation login ($username: String!, $password: String!) {\r\n  login(username: $username, password: $password) {\r\n    message\r\n    user {\r\n      id\r\n      username\r\n      email\r\n      token\r\n    }\r\n  }\r\n}\r\n`;\r\n\r\nexport const SIGNUP_MUTATION = gql`\r\n  mutation signup($username: String!, $email: String!, $password: String!) {\r\n    signup(username: $username, email: $email, password: $password) {\r\n      message\r\n      user {\r\n        id\r\n        username\r\n        email\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nexport const ADD_TO_CART_MUTATION = gql`\r\n  mutation AddToCart($productId: ID!, $quantity: Int!, $selectedAttributes: String) {\r\n    addToCart(productId: $productId, quantity: $quantity, selectedAttributes: $selectedAttributes) {\r\n      id\r\n      product {\r\n        id\r\n        name\r\n        price\r\n        image\r\n        attributes\r\n      }\r\n      quantity\r\n      selectedAttributes\r\n    }\r\n  }\r\n`;\r\n\r\n// Mutation to update cart item quantity\r\nexport const UPDATE_CART_MUTATION = gql`\r\n  mutation updateCart($itemId: ID!, $quantityChange: Int!) {\r\n    updateCart(itemId: $itemId, quantityChange: $quantityChange) {\r\n      id\r\n      quantity\r\n    }\r\n  }\r\n`;\r\n\r\n// Mutation to remove an item from the cart\r\nexport const REMOVE_FROM_CART_MUTATION = gql`\r\n  mutation RemoveFromCart($itemId: ID!) {\r\n    removeFromCart(itemId: $itemId) {\r\n      id\r\n      product {\r\n        id\r\n        name\r\n        price\r\n        image\r\n      }\r\n      quantity\r\n    }\r\n  }\r\n`;\r\n\r\n// Mutation to place an order\r\nexport const PLACE_ORDER_MUTATION = gql`\r\n  mutation PlaceOrder {\r\n    placeOrder {\r\n      success\r\n      message\r\n    }\r\n  }\r\n`;\r\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,OAAO,MAAMC,cAAc,GAAGD,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAME,eAAe,GAAGF,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMG,oBAAoB,GAAGH,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,OAAO,MAAMI,oBAAoB,GAAGJ,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,OAAO,MAAMK,yBAAyB,GAAGL,GAAG;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,OAAO,MAAMM,oBAAoB,GAAGN,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}