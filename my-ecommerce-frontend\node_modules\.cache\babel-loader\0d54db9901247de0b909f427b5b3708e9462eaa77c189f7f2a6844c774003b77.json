{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\components\\\\ProductList.js\",\n  _s = $RefreshSig$();\n// src/components/ProductList.js\nimport { useQuery, useMutation } from '@apollo/client';\nimport { GET_PRODUCTS, GET_CART_QUERY } from '../graphql/queries';\nimport { ADD_TO_CART_MUTATION } from '../graphql/mutations';\nimport { Link } from 'react-router-dom';\nimport cart from '../assets/img/Vector.svg';\nimport '../styles/ProductList.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ProductList({\n  category_id,\n  category\n}) {\n  _s();\n  const {\n    loading,\n    error,\n    data\n  } = useQuery(GET_PRODUCTS);\n  const [addToCart] = useMutation(ADD_TO_CART_MUTATION, {\n    refetchQueries: [{\n      query: GET_CART_QUERY\n    }],\n    awaitRefetchQueries: true,\n    onCompleted: () => {\n      alert('Product added to cart!');\n    },\n    onError: error => {\n      console.error('Add to cart error:', error);\n      alert('Failed to add product to cart. Please try again.');\n    },\n    errorPolicy: 'all'\n  });\n  if (loading) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: [\"Error: \", error.message]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 21\n  }, this);\n  const products = category ? data.products.filter(product => product.category_id === category_id) : data.products;\n\n  // Helper function to convert product name to kebab case\n  const toKebabCase = str => {\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n  };\n\n  // Handle quick shop (add to cart with default options)\n  const handleQuickShop = (e, productId) => {\n    e.preventDefault(); // Prevent navigation to product details\n    e.stopPropagation(); // Stop event bubbling\n\n    addToCart({\n      variables: {\n        productId: productId,\n        quantity: 1\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"products\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: category ? category : \"All\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 7\n      }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-card\",\n        \"data-testid\": `product-${toKebabCase(product.name)}`,\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          className: \"product-link\",\n          to: `/product/${product.id}`,\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"product-card__image\",\n            src: product.image_url,\n            alt: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"product-card__brand\",\n            children: product.brand\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-name\",\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"price\",\n            children: [\"$\", product.amount]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"quick-shop-btn\",\n          onClick: e => handleQuickShop(e, product.id),\n          \"data-testid\": `add-to-cart-${toKebabCase(product.name)}`,\n          title: \"Quick Shop\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: cart,\n            width: \"20\",\n            height: \"20\",\n            alt: \"Add to Cart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this)]\n      }, product.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 5\n    }, this)\n  }, void 0, false);\n}\n_s(ProductList, \"5aYdY5sPulAcDm7B8lK39farg3A=\", false, function () {\n  return [useQuery, useMutation];\n});\n_c = ProductList;\nexport default ProductList;\nvar _c;\n$RefreshReg$(_c, \"ProductList\");", "map": {"version": 3, "names": ["useQuery", "useMutation", "GET_PRODUCTS", "GET_CART_QUERY", "ADD_TO_CART_MUTATION", "Link", "cart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductList", "category_id", "category", "_s", "loading", "error", "data", "addToCart", "refetchQueries", "query", "awaitRefetchQueries", "onCompleted", "alert", "onError", "console", "errorPolicy", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "products", "filter", "product", "toKebabCase", "str", "toLowerCase", "replace", "handleQuickShop", "e", "productId", "preventDefault", "stopPropagation", "variables", "quantity", "className", "map", "name", "to", "id", "src", "image_url", "alt", "brand", "amount", "onClick", "title", "width", "height", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/components/ProductList.js"], "sourcesContent": ["// src/components/ProductList.js\r\nimport { useQuery, useMutation } from '@apollo/client';\r\nimport { GET_PRODUCTS, GET_CART_QUERY } from '../graphql/queries';\r\nimport { ADD_TO_CART_MUTATION } from '../graphql/mutations';\r\nimport { Link } from 'react-router-dom';\r\nimport cart from '../assets/img/Vector.svg';\r\nimport '../styles/ProductList.css';\r\n\r\nfunction ProductList({ category_id, category }) {\r\n  const { loading, error, data } = useQuery(GET_PRODUCTS);\r\n\r\n  const [addToCart] = useMutation(ADD_TO_CART_MUTATION, {\r\n    refetchQueries: [{ query: GET_CART_QUERY }],\r\n    awaitRefetchQueries: true,\r\n    onCompleted: () => {\r\n      alert('Product added to cart!');\r\n    },\r\n    onError: (error) => {\r\n      console.error('Add to cart error:', error);\r\n      alert('Failed to add product to cart. Please try again.');\r\n    },\r\n    errorPolicy: 'all'\r\n  });\r\n\r\n  if (loading) return <p>Loading...</p>;\r\n  if (error) return <p>Error: {error.message}</p>;\r\n\r\n  const products = category\r\n    ? data.products.filter(product => product.category_id === category_id)\r\n    : data.products;\r\n\r\n  // Helper function to convert product name to kebab case\r\n  const toKebabCase = (str) => {\r\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\r\n  };\r\n\r\n  // Handle quick shop (add to cart with default options)\r\n  const handleQuickShop = (e, productId) => {\r\n    e.preventDefault(); // Prevent navigation to product details\r\n    e.stopPropagation(); // Stop event bubbling\r\n\r\n    addToCart({\r\n      variables: {\r\n        productId: productId,\r\n        quantity: 1,\r\n      },\r\n    });\r\n  };\r\n\r\n  return (\r\n    <>\r\n    <div className=\"products\">\r\n      <div className=\"header\">\r\n        <h1>{category ? category: \"All\"}</h1>\r\n      </div>\r\n        {products.map((product) => (\r\n          <div className='product-card' data-testid={`product-${toKebabCase(product.name)}`} key={product.id}>\r\n            <Link className='product-link' to={`/product/${product.id}`}>\r\n              <img className=\"product-card__image\" src={product.image_url} alt={product.name} />\r\n              <p className=\"product-card__brand\">{product.brand}</p>\r\n              <div className=\"product-name\">{product.name}</div>\r\n              <div className=\"price\">\r\n                ${product.amount}\r\n              </div>\r\n            </Link>\r\n\r\n            {/* Quick Shop Button - only visible on hover */}\r\n            <button\r\n              className=\"quick-shop-btn\"\r\n              onClick={(e) => handleQuickShop(e, product.id)}\r\n              data-testid={`add-to-cart-${toKebabCase(product.name)}`}\r\n              title=\"Quick Shop\"\r\n            >\r\n              <img src={cart} width=\"20\" height=\"20\" alt=\"Add to Cart\" />\r\n            </button>\r\n          </div>\r\n        ))}\r\n    </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default ProductList;\r\n"], "mappings": ";;AAAA;AACA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,gBAAgB;AACtD,SAASC,YAAY,EAAEC,cAAc,QAAQ,oBAAoB;AACjE,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnC,SAASC,WAAWA,CAAC;EAAEC,WAAW;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAC9C,MAAM;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGjB,QAAQ,CAACE,YAAY,CAAC;EAEvD,MAAM,CAACgB,SAAS,CAAC,GAAGjB,WAAW,CAACG,oBAAoB,EAAE;IACpDe,cAAc,EAAE,CAAC;MAAEC,KAAK,EAAEjB;IAAe,CAAC,CAAC;IAC3CkB,mBAAmB,EAAE,IAAI;IACzBC,WAAW,EAAEA,CAAA,KAAM;MACjBC,KAAK,CAAC,wBAAwB,CAAC;IACjC,CAAC;IACDC,OAAO,EAAGR,KAAK,IAAK;MAClBS,OAAO,CAACT,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CO,KAAK,CAAC,kDAAkD,CAAC;IAC3D,CAAC;IACDG,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,IAAIX,OAAO,EAAE,oBAAOP,OAAA;IAAAmB,QAAA,EAAG;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACrC,IAAIf,KAAK,EAAE,oBAAOR,OAAA;IAAAmB,QAAA,GAAG,SAAO,EAACX,KAAK,CAACgB,OAAO;EAAA;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;EAE/C,MAAME,QAAQ,GAAGpB,QAAQ,GACrBI,IAAI,CAACgB,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACvB,WAAW,KAAKA,WAAW,CAAC,GACpEK,IAAI,CAACgB,QAAQ;;EAEjB;EACA,MAAMG,WAAW,GAAIC,GAAG,IAAK;IAC3B,OAAOA,GAAG,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;EAC1E,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,CAAC,EAAEC,SAAS,KAAK;IACxCD,CAAC,CAACE,cAAc,CAAC,CAAC,CAAC,CAAC;IACpBF,CAAC,CAACG,eAAe,CAAC,CAAC,CAAC,CAAC;;IAErB1B,SAAS,CAAC;MACR2B,SAAS,EAAE;QACTH,SAAS,EAAEA,SAAS;QACpBI,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC;EACJ,CAAC;EAED,oBACEtC,OAAA,CAAAE,SAAA;IAAAiB,QAAA,eACAnB,OAAA;MAAKuC,SAAS,EAAC,UAAU;MAAApB,QAAA,gBACvBnB,OAAA;QAAKuC,SAAS,EAAC,QAAQ;QAAApB,QAAA,eACrBnB,OAAA;UAAAmB,QAAA,EAAKd,QAAQ,GAAGA,QAAQ,GAAE;QAAK;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,EACHE,QAAQ,CAACe,GAAG,CAAEb,OAAO,iBACpB3B,OAAA;QAAKuC,SAAS,EAAC,cAAc;QAAC,eAAa,WAAWX,WAAW,CAACD,OAAO,CAACc,IAAI,CAAC,EAAG;QAAAtB,QAAA,gBAChFnB,OAAA,CAACH,IAAI;UAAC0C,SAAS,EAAC,cAAc;UAACG,EAAE,EAAE,YAAYf,OAAO,CAACgB,EAAE,EAAG;UAAAxB,QAAA,gBAC1DnB,OAAA;YAAKuC,SAAS,EAAC,qBAAqB;YAACK,GAAG,EAAEjB,OAAO,CAACkB,SAAU;YAACC,GAAG,EAAEnB,OAAO,CAACc;UAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClFvB,OAAA;YAAGuC,SAAS,EAAC,qBAAqB;YAAApB,QAAA,EAAEQ,OAAO,CAACoB;UAAK;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtDvB,OAAA;YAAKuC,SAAS,EAAC,cAAc;YAAApB,QAAA,EAAEQ,OAAO,CAACc;UAAI;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClDvB,OAAA;YAAKuC,SAAS,EAAC,OAAO;YAAApB,QAAA,GAAC,GACpB,EAACQ,OAAO,CAACqB,MAAM;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPvB,OAAA;UACEuC,SAAS,EAAC,gBAAgB;UAC1BU,OAAO,EAAGhB,CAAC,IAAKD,eAAe,CAACC,CAAC,EAAEN,OAAO,CAACgB,EAAE,CAAE;UAC/C,eAAa,eAAef,WAAW,CAACD,OAAO,CAACc,IAAI,CAAC,EAAG;UACxDS,KAAK,EAAC,YAAY;UAAA/B,QAAA,eAElBnB,OAAA;YAAK4C,GAAG,EAAE9C,IAAK;YAACqD,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACN,GAAG,EAAC;UAAa;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA,GAlB6EI,OAAO,CAACgB,EAAE;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmB7F,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC,gBACJ,CAAC;AAEP;AAACjB,EAAA,CAxEQH,WAAW;EAAA,QACeX,QAAQ,EAErBC,WAAW;AAAA;AAAA4D,EAAA,GAHxBlD,WAAW;AA0EpB,eAAeA,WAAW;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}