[{"D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\index.js": "1", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\apolloClient.js": "2", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\App.js": "3", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\CartPopup.js": "4", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\ProductDetails.js": "5", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Navbar.js": "6", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\ProductList.js": "7", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Button.js": "8", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\graphql\\mutations.js": "9", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\graphql\\queries.js": "10"}, {"size": 325, "mtime": 1752975046932, "results": "11", "hashOfConfig": "12"}, {"size": 1491, "mtime": 1752974650937, "results": "13", "hashOfConfig": "12"}, {"size": 836, "mtime": 1752975031605, "results": "14", "hashOfConfig": "12"}, {"size": 11012, "mtime": 1752978691972, "results": "15", "hashOfConfig": "12"}, {"size": 6873, "mtime": 1752978646109, "results": "16", "hashOfConfig": "12"}, {"size": 1867, "mtime": 1752974940934, "results": "17", "hashOfConfig": "12"}, {"size": 3143, "mtime": 1752978660151, "results": "18", "hashOfConfig": "12"}, {"size": 397, "mtime": 1752974907664, "results": "19", "hashOfConfig": "12"}, {"size": 1699, "mtime": 1752978621882, "results": "20", "hashOfConfig": "12"}, {"size": 792, "mtime": 1752978633133, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10vzdib", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\index.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\apolloClient.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\App.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\CartPopup.js", ["52"], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\ProductDetails.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Navbar.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\ProductList.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Button.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\graphql\\mutations.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\graphql\\queries.js", [], [], {"ruleId": "53", "severity": 1, "message": "54", "line": 14, "column": 10, "nodeType": "55", "messageId": "56", "endLine": 14, "endColumn": 25}, "no-unused-vars", "'selectedOptions' is assigned a value but never used.", "Identifier", "unusedVar"]