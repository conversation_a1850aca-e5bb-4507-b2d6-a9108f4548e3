{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\components\\\\CartPopup.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { useQuery, useMutation } from '@apollo/client';\nimport { GET_CART_QUERY } from '../graphql/queries';\nimport { UPDATE_CART_MUTATION } from '../graphql/mutations';\nimport '../styles/CartPopup.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CartPopup({\n  isOpen,\n  closePopup,\n  cartItems\n}) {\n  _s();\n  const {\n    loading,\n    error,\n    data,\n    refetch\n  } = useQuery(GET_CART_QUERY, {\n    fetchPolicy: 'cache-and-network',\n    notifyOnNetworkStatusChange: true\n  });\n\n  // State for managing selected options for each cart item\n  const [selectedOptions] = useState({});\n\n  // Mutations\n  const [updateCartItem] = useMutation(UPDATE_CART_MUTATION, {\n    onError: error => {\n      console.error('Update cart error:', error);\n    },\n    errorPolicy: 'all'\n  });\n\n  // Prevent rendering if the popup is closed\n  if (!isOpen) return null;\n\n  // Show loading state\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"cart-popup\",\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 23\n  }, this);\n\n  // Show error state\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"cart-popup\",\n    children: [\"Error loading cart: \", error.message]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 21\n  }, this);\n\n  // Use data from query instead of props for consistency\n  const actualCartItems = (data === null || data === void 0 ? void 0 : data.cart) || cartItems || [];\n  const calculateTotal = () => {\n    if (!actualCartItems || !Array.isArray(actualCartItems)) return '0.00';\n    return actualCartItems.reduce((total, item) => total + item.product.price * item.quantity, 0).toFixed(2);\n  };\n  const getTotalItemCount = () => {\n    if (!actualCartItems || !Array.isArray(actualCartItems)) return 0;\n    return actualCartItems.reduce((total, item) => total + item.quantity, 0);\n  };\n\n  // Helper function to convert attribute name to kebab case\n  const toKebabCase = str => {\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n  };\n\n  // Note: Size and color selection are non-functional in cart as per requirements\n  // Attributes are display-only\n\n  // Get selected options for an item from cart data or defaults\n  const getSelectedOptions = item => {\n    // First try to get from cart item's selectedAttributes\n    if (item.selectedAttributes) {\n      try {\n        const parsed = JSON.parse(item.selectedAttributes);\n        return {\n          size: parsed.Size || parsed.size || 'S',\n          color: parsed.Color || parsed.color || '#2B5D31'\n        };\n      } catch (error) {\n        console.error('Error parsing selectedAttributes:', error);\n      }\n    }\n\n    // Fallback to defaults based on available attributes\n    const parsedAttributes = parseProductAttributes(item.product.attributes);\n    const availableSizes = parsedAttributes.Size || parsedAttributes.size || ['S'];\n    const availableColors = parsedAttributes.Color || parsedAttributes.color || ['#2B5D31'];\n    return {\n      size: availableSizes[0] || 'S',\n      color: availableColors[0] || '#2B5D31'\n    };\n  };\n\n  // Parse product attributes\n  const parseProductAttributes = attributesString => {\n    try {\n      if (!attributesString) return {};\n      return JSON.parse(attributesString);\n    } catch (error) {\n      return {};\n    }\n  };\n\n  // Get attribute options for display\n  const getAttributeOptions = (attributes, attributeName) => {\n    const parsedAttributes = parseProductAttributes(attributes);\n    return parsedAttributes[attributeName] || [];\n  };\n\n  // Increase Quantity Handler\n  const handleIncreaseQuantity = itemId => {\n    updateCartItem({\n      variables: {\n        itemId,\n        quantityChange: 1\n      },\n      refetchQueries: [{\n        query: GET_CART_QUERY\n      }]\n    }).catch(error => {\n      console.error('Error increasing quantity:', error);\n      alert('Failed to update quantity. Please try again.');\n    });\n  };\n\n  // Decrease Quantity Handler - now removes item when quantity reaches 0\n  const handleDecreaseQuantity = (itemId, currentQuantity) => {\n    if (currentQuantity > 1) {\n      // Decrease quantity by 1\n      updateCartItem({\n        variables: {\n          itemId,\n          quantityChange: -1\n        },\n        refetchQueries: [{\n          query: GET_CART_QUERY\n        }]\n      }).catch(error => {\n        console.error('Error decreasing quantity:', error);\n        alert('Failed to update quantity. Please try again.');\n      });\n    } else {\n      // When quantity is 1, decrease it to 0 which should remove the item\n      updateCartItem({\n        variables: {\n          itemId,\n          quantityChange: -1\n        },\n        refetchQueries: [{\n          query: GET_CART_QUERY\n        }],\n        awaitRefetchQueries: true\n      }).catch(error => {\n        console.error('Error removing item via quantity decrease:', error);\n        alert('Failed to remove item. Please try again.');\n      });\n    }\n  };\n\n  // Place Order Handler using dedicated endpoint\n  const handlePlaceOrder = async () => {\n    try {\n      var _result$data, _result$data$placeOrd;\n      const response = await fetch('http://localhost:8000/place_order_endpoint.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          query: `\n            mutation PlaceOrder {\n              placeOrder {\n                success\n                message\n              }\n            }\n          `\n        })\n      });\n      const result = await response.json();\n      if ((_result$data = result.data) !== null && _result$data !== void 0 && (_result$data$placeOrd = _result$data.placeOrder) !== null && _result$data$placeOrd !== void 0 && _result$data$placeOrd.success) {\n        // Refetch the cart data to update UI\n        await refetch();\n        alert('Order placed successfully!');\n        closePopup(); // Close the cart popup\n      } else {\n        var _result$data2, _result$data2$placeOr;\n        throw new Error(((_result$data2 = result.data) === null || _result$data2 === void 0 ? void 0 : (_result$data2$placeOr = _result$data2.placeOrder) === null || _result$data2$placeOr === void 0 ? void 0 : _result$data2$placeOr.message) || 'Order failed');\n      }\n    } catch (err) {\n      console.error('Place order error:', err);\n      alert(`Failed to place order: ${err.message}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modalBackground\",\n    onClick: closePopup,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modalContainer\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"My Bag, \", getTotalItemCount(), \" items\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-btn\",\n          onClick: closePopup,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), !actualCartItems || actualCartItems.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Your cart is empty.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-items-container\",\n          children: actualCartItems === null || actualCartItems === void 0 ? void 0 : actualCartItems.map((item, index) => {\n            const itemOptions = getSelectedOptions(item.id, item.product.attributes);\n\n            // Get available sizes and colors from product attributes\n            const availableSizes = getAttributeOptions(item.product.attributes, 'Size') || getAttributeOptions(item.product.attributes, 'size') || ['XS', 'S', 'M', 'L']; // fallback\n            const availableColors = getAttributeOptions(item.product.attributes, 'Color') || getAttributeOptions(item.product.attributes, 'color') || ['#C4D79B', '#2B5D31', '#0F4C3A']; // fallback\n\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cart-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cart-item-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"product-name\",\n                  children: item.product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"product-price\",\n                  \"data-testid\": \"cart-item-amount\",\n                  children: [\"$\", item.product.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 23\n                }, this), availableSizes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"size-section\",\n                  \"data-testid\": `cart-item-attribute-${toKebabCase('Size')}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"size-label\",\n                    children: \"Size:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-options\",\n                    children: availableSizes.map(size => /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `size-btn ${itemOptions.size === size ? 'selected' : ''} non-clickable`,\n                      \"data-testid\": `cart-item-attribute-${toKebabCase('Size')}-${toKebabCase(size)}${itemOptions.size === size ? '-selected' : ''}`,\n                      children: size\n                    }, size, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 25\n                }, this), availableColors.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"color-section\",\n                  \"data-testid\": `cart-item-attribute-${toKebabCase('Color')}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"color-label\",\n                    children: \"Color:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"color-options\",\n                    children: availableColors.map(color => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `color-circle ${itemOptions.color === color ? 'selected' : ''} non-clickable`,\n                      style: {\n                        backgroundColor: color\n                      },\n                      \"data-testid\": `cart-item-attribute-${toKebabCase('Color')}-${toKebabCase(color)}${itemOptions.color === color ? '-selected' : ''}`\n                    }, color, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cart-item-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"cart-item-image\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: item.product.image,\n                    alt: item.product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"quantity-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"cart-item-controls\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"quantity-btn\",\n                      \"data-testid\": \"cart-item-amount-decrease\",\n                      onClick: () => handleDecreaseQuantity(item.id, item.quantity),\n                      children: \"-\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"quantity-display\",\n                      children: item.quantity\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"quantity-btn\",\n                      \"data-testid\": \"cart-item-amount-increase\",\n                      onClick: () => handleIncreaseQuantity(item.id),\n                      children: \"+\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"item-number\",\n                    children: index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"total-section\",\n          \"data-testid\": \"cart-total\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"$\", calculateTotal()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePlaceOrder,\n          className: \"place-order-btn\",\n          children: \"Place Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n}\n_s(CartPopup, \"XA1R+4cd2NpxkRnH8lMXfqSoLNI=\", false, function () {\n  return [useQuery, useMutation];\n});\n_c = CartPopup;\nexport default CartPopup;\nvar _c;\n$RefreshReg$(_c, \"CartPopup\");", "map": {"version": 3, "names": ["useState", "useQuery", "useMutation", "GET_CART_QUERY", "UPDATE_CART_MUTATION", "jsxDEV", "_jsxDEV", "CartPopup", "isOpen", "closePopup", "cartItems", "_s", "loading", "error", "data", "refetch", "fetchPolicy", "notifyOnNetworkStatusChange", "selectedOptions", "updateCartItem", "onError", "console", "errorPolicy", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "actualCartItems", "cart", "calculateTotal", "Array", "isArray", "reduce", "total", "item", "product", "price", "quantity", "toFixed", "getTotalItemCount", "toKebabCase", "str", "toLowerCase", "replace", "getSelectedOptions", "selectedAttributes", "parsed", "JSON", "parse", "size", "Size", "color", "Color", "parsedAttributes", "parseProductAttributes", "attributes", "availableSizes", "availableColors", "attributesString", "getAttributeOptions", "attributeName", "handleIncreaseQuantity", "itemId", "variables", "quantityChange", "refetchQueries", "query", "catch", "alert", "handleDecreaseQuantity", "currentQuantity", "awaitRefetchQueries", "handlePlaceOrder", "_result$data", "_result$data$placeOrd", "response", "fetch", "method", "headers", "body", "stringify", "result", "json", "placeOrder", "success", "_result$data2", "_result$data2$placeOr", "Error", "err", "onClick", "e", "stopPropagation", "length", "map", "index", "itemOptions", "id", "name", "style", "backgroundColor", "src", "image", "alt", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/components/CartPopup.js"], "sourcesContent": ["import { useState } from 'react';\r\nimport { useQuery, useMutation } from '@apollo/client';\r\nimport { GET_CART_QUERY } from '../graphql/queries';\r\nimport { UPDATE_CART_MUTATION } from '../graphql/mutations';\r\nimport '../styles/CartPopup.css';\r\n\r\nfunction CartPopup({ isOpen, closePopup, cartItems }) {\r\n  const { loading, error, data, refetch } = useQuery(GET_CART_QUERY, {\r\n    fetchPolicy: 'cache-and-network',\r\n    notifyOnNetworkStatusChange: true\r\n  });\r\n\r\n  // State for managing selected options for each cart item\r\n  const [selectedOptions] = useState({});\r\n\r\n\r\n\r\n\r\n  // Mutations\r\n  const [updateCartItem] = useMutation(UPDATE_CART_MUTATION, {\r\n    onError: (error) => {\r\n      console.error('Update cart error:', error);\r\n    },\r\n    errorPolicy: 'all'\r\n  });\r\n\r\n\r\n\r\n  // Prevent rendering if the popup is closed\r\n  if (!isOpen) return null;\r\n\r\n  // Show loading state\r\n  if (loading) return <div className=\"cart-popup\">Loading...</div>;\r\n\r\n  // Show error state\r\n  if (error) return <div className=\"cart-popup\">Error loading cart: {error.message}</div>;\r\n\r\n  // Use data from query instead of props for consistency\r\n  const actualCartItems = data?.cart || cartItems || [];\r\n\r\n  const calculateTotal = () => {\r\n    if (!actualCartItems || !Array.isArray(actualCartItems)) return '0.00';\r\n    return actualCartItems.reduce((total, item) => total + item.product.price * item.quantity, 0).toFixed(2);\r\n  };\r\n\r\n  const getTotalItemCount = () => {\r\n    if (!actualCartItems || !Array.isArray(actualCartItems)) return 0;\r\n    return actualCartItems.reduce((total, item) => total + item.quantity, 0);\r\n  };\r\n\r\n  // Helper function to convert attribute name to kebab case\r\n  const toKebabCase = (str) => {\r\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\r\n  };\r\n\r\n  // Note: Size and color selection are non-functional in cart as per requirements\r\n  // Attributes are display-only\r\n\r\n  // Get selected options for an item from cart data or defaults\r\n  const getSelectedOptions = (item) => {\r\n    // First try to get from cart item's selectedAttributes\r\n    if (item.selectedAttributes) {\r\n      try {\r\n        const parsed = JSON.parse(item.selectedAttributes);\r\n        return {\r\n          size: parsed.Size || parsed.size || 'S',\r\n          color: parsed.Color || parsed.color || '#2B5D31'\r\n        };\r\n      } catch (error) {\r\n        console.error('Error parsing selectedAttributes:', error);\r\n      }\r\n    }\r\n\r\n    // Fallback to defaults based on available attributes\r\n    const parsedAttributes = parseProductAttributes(item.product.attributes);\r\n    const availableSizes = parsedAttributes.Size || parsedAttributes.size || ['S'];\r\n    const availableColors = parsedAttributes.Color || parsedAttributes.color || ['#2B5D31'];\r\n\r\n    return {\r\n      size: availableSizes[0] || 'S',\r\n      color: availableColors[0] || '#2B5D31'\r\n    };\r\n  };\r\n\r\n  // Parse product attributes\r\n  const parseProductAttributes = (attributesString) => {\r\n    try {\r\n      if (!attributesString) return {};\r\n      return JSON.parse(attributesString);\r\n    } catch (error) {\r\n      return {};\r\n    }\r\n  };\r\n\r\n  // Get attribute options for display\r\n  const getAttributeOptions = (attributes, attributeName) => {\r\n    const parsedAttributes = parseProductAttributes(attributes);\r\n    return parsedAttributes[attributeName] || [];\r\n  };\r\n\r\n  // Increase Quantity Handler\r\n  const handleIncreaseQuantity = (itemId) => {\r\n    updateCartItem({\r\n      variables: { itemId, quantityChange: 1 },\r\n      refetchQueries: [{ query: GET_CART_QUERY }],\r\n    })\r\n    .catch((error) => {\r\n      console.error('Error increasing quantity:', error);\r\n      alert('Failed to update quantity. Please try again.');\r\n    });\r\n  };\r\n\r\n  // Decrease Quantity Handler - now removes item when quantity reaches 0\r\n  const handleDecreaseQuantity = (itemId, currentQuantity) => {\r\n    if (currentQuantity > 1) {\r\n      // Decrease quantity by 1\r\n      updateCartItem({\r\n        variables: { itemId, quantityChange: -1 },\r\n        refetchQueries: [{ query: GET_CART_QUERY }],\r\n      })\r\n      .catch((error) => {\r\n        console.error('Error decreasing quantity:', error);\r\n        alert('Failed to update quantity. Please try again.');\r\n      });\r\n    } else {\r\n      // When quantity is 1, decrease it to 0 which should remove the item\r\n      updateCartItem({\r\n        variables: { itemId, quantityChange: -1 },\r\n        refetchQueries: [{ query: GET_CART_QUERY }],\r\n        awaitRefetchQueries: true\r\n      })\r\n      .catch((error) => {\r\n        console.error('Error removing item via quantity decrease:', error);\r\n        alert('Failed to remove item. Please try again.');\r\n      });\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Place Order Handler using dedicated endpoint\r\n  const handlePlaceOrder = async () => {\r\n    try {\r\n      const response = await fetch('http://localhost:8000/place_order_endpoint.php', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          query: `\r\n            mutation PlaceOrder {\r\n              placeOrder {\r\n                success\r\n                message\r\n              }\r\n            }\r\n          `\r\n        })\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.data?.placeOrder?.success) {\r\n        // Refetch the cart data to update UI\r\n        await refetch();\r\n        alert('Order placed successfully!');\r\n        closePopup(); // Close the cart popup\r\n      } else {\r\n        throw new Error(result.data?.placeOrder?.message || 'Order failed');\r\n      }\r\n\r\n    } catch (err) {\r\n      console.error('Place order error:', err);\r\n      alert(`Failed to place order: ${err.message}`);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"modalBackground\" onClick={closePopup}>\r\n      <div className=\"modalContainer\" onClick={(e) => e.stopPropagation()}>\r\n        <div className=\"cart-header\">\r\n          <h2>My Bag, {getTotalItemCount()} items</h2>\r\n          <button className=\"close-btn\" onClick={closePopup}>×</button>\r\n        </div>\r\n        {!actualCartItems || actualCartItems.length === 0 ? (\r\n          <p>Your cart is empty.</p>\r\n        ) : (\r\n          <div className=\"cart-content\">\r\n            <div className=\"cart-items-container\">\r\n              {actualCartItems?.map((item, index) => {\r\n                const itemOptions = getSelectedOptions(item.id, item.product.attributes);\r\n\r\n                // Get available sizes and colors from product attributes\r\n                const availableSizes = getAttributeOptions(item.product.attributes, 'Size') ||\r\n                                     getAttributeOptions(item.product.attributes, 'size') ||\r\n                                     ['XS', 'S', 'M', 'L']; // fallback\r\n                const availableColors = getAttributeOptions(item.product.attributes, 'Color') ||\r\n                                       getAttributeOptions(item.product.attributes, 'color') ||\r\n                                       ['#C4D79B', '#2B5D31', '#0F4C3A']; // fallback\r\n\r\n\r\n\r\n                return (\r\n                  <div key={item.id} className=\"cart-item\">\r\n                    <div className=\"cart-item-left\">\r\n                      <h3 className=\"product-name\">{item.product.name}</h3>\r\n                      <p className=\"product-price\" data-testid='cart-item-amount'>${item.product.price}</p>\r\n\r\n                      {availableSizes.length > 0 && (\r\n                        <div className=\"size-section\" data-testid={`cart-item-attribute-${toKebabCase('Size')}`}>\r\n                          <span className=\"size-label\">Size:</span>\r\n                          <div className=\"size-options\">\r\n                            {availableSizes.map(size => (\r\n                              <span\r\n                                key={size}\r\n                                className={`size-btn ${itemOptions.size === size ? 'selected' : ''} non-clickable`}\r\n                                data-testid={`cart-item-attribute-${toKebabCase('Size')}-${toKebabCase(size)}${itemOptions.size === size ? '-selected' : ''}`}\r\n                              >\r\n                                {size}\r\n                              </span>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {availableColors.length > 0 && (\r\n                        <div className=\"color-section\" data-testid={`cart-item-attribute-${toKebabCase('Color')}`}>\r\n                          <span className=\"color-label\">Color:</span>\r\n                          <div className=\"color-options\">\r\n                            {availableColors.map(color => (\r\n                              <div\r\n                                key={color}\r\n                                className={`color-circle ${itemOptions.color === color ? 'selected' : ''} non-clickable`}\r\n                                style={{backgroundColor: color}}\r\n                                data-testid={`cart-item-attribute-${toKebabCase('Color')}-${toKebabCase(color)}${itemOptions.color === color ? '-selected' : ''}`}\r\n                              ></div>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                  <div className=\"cart-item-right\">\r\n                    <div className=\"cart-item-image\">\r\n                      <img src={item.product.image} alt={item.product.name} />\r\n                    </div>\r\n                    <div className=\"quantity-section\">\r\n                      <div className=\"cart-item-controls\">\r\n                        <button\r\n                          className=\"quantity-btn\"\r\n                          data-testid='cart-item-amount-decrease'\r\n                          onClick={() => handleDecreaseQuantity(item.id, item.quantity)}\r\n                        >\r\n                          -\r\n                        </button>\r\n                        <span className=\"quantity-display\">{item.quantity}</span>\r\n                        <button\r\n                          className=\"quantity-btn\"\r\n                          data-testid='cart-item-amount-increase'\r\n                          onClick={() => handleIncreaseQuantity(item.id)}\r\n                        >\r\n                          +\r\n                        </button>\r\n                      </div>\r\n                      <div className=\"item-number\">{index + 1}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                );\r\n              })}\r\n            </div>\r\n            {/* Total Section */}\r\n            <div className=\"total-section\" data-testid=\"cart-total\">\r\n              <p>Total</p>\r\n              <p>${calculateTotal()}</p>\r\n            </div>\r\n            <button onClick={handlePlaceOrder} className=\"place-order-btn\">\r\n              Place Order\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default CartPopup;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,gBAAgB;AACtD,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,SAASC,SAASA,CAAC;EAAEC,MAAM;EAAEC,UAAU;EAAEC;AAAU,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM;IAAEC,OAAO;IAAEC,KAAK;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGd,QAAQ,CAACE,cAAc,EAAE;IACjEa,WAAW,EAAE,mBAAmB;IAChCC,2BAA2B,EAAE;EAC/B,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAKtC;EACA,MAAM,CAACmB,cAAc,CAAC,GAAGjB,WAAW,CAACE,oBAAoB,EAAE;IACzDgB,OAAO,EAAGP,KAAK,IAAK;MAClBQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C,CAAC;IACDS,WAAW,EAAE;EACf,CAAC,CAAC;;EAIF;EACA,IAAI,CAACd,MAAM,EAAE,OAAO,IAAI;;EAExB;EACA,IAAII,OAAO,EAAE,oBAAON,OAAA;IAAKiB,SAAS,EAAC,YAAY;IAAAC,QAAA,EAAC;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;;EAEhE;EACA,IAAIf,KAAK,EAAE,oBAAOP,OAAA;IAAKiB,SAAS,EAAC,YAAY;IAAAC,QAAA,GAAC,sBAAoB,EAACX,KAAK,CAACgB,OAAO;EAAA;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;;EAEvF;EACA,MAAME,eAAe,GAAG,CAAAhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,IAAI,KAAIrB,SAAS,IAAI,EAAE;EAErD,MAAMsB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACF,eAAe,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,eAAe,CAAC,EAAE,OAAO,MAAM;IACtE,OAAOA,eAAe,CAACK,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACC,OAAO,CAACC,KAAK,GAAGF,IAAI,CAACG,QAAQ,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAC1G,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACZ,eAAe,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,eAAe,CAAC,EAAE,OAAO,CAAC;IACjE,OAAOA,eAAe,CAACK,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACG,QAAQ,EAAE,CAAC,CAAC;EAC1E,CAAC;;EAED;EACA,MAAMG,WAAW,GAAIC,GAAG,IAAK;IAC3B,OAAOA,GAAG,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;EAC1E,CAAC;;EAED;EACA;;EAEA;EACA,MAAMC,kBAAkB,GAAIV,IAAI,IAAK;IACnC;IACA,IAAIA,IAAI,CAACW,kBAAkB,EAAE;MAC3B,IAAI;QACF,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACd,IAAI,CAACW,kBAAkB,CAAC;QAClD,OAAO;UACLI,IAAI,EAAEH,MAAM,CAACI,IAAI,IAAIJ,MAAM,CAACG,IAAI,IAAI,GAAG;UACvCE,KAAK,EAAEL,MAAM,CAACM,KAAK,IAAIN,MAAM,CAACK,KAAK,IAAI;QACzC,CAAC;MACH,CAAC,CAAC,OAAOzC,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;IACF;;IAEA;IACA,MAAM2C,gBAAgB,GAAGC,sBAAsB,CAACpB,IAAI,CAACC,OAAO,CAACoB,UAAU,CAAC;IACxE,MAAMC,cAAc,GAAGH,gBAAgB,CAACH,IAAI,IAAIG,gBAAgB,CAACJ,IAAI,IAAI,CAAC,GAAG,CAAC;IAC9E,MAAMQ,eAAe,GAAGJ,gBAAgB,CAACD,KAAK,IAAIC,gBAAgB,CAACF,KAAK,IAAI,CAAC,SAAS,CAAC;IAEvF,OAAO;MACLF,IAAI,EAAEO,cAAc,CAAC,CAAC,CAAC,IAAI,GAAG;MAC9BL,KAAK,EAAEM,eAAe,CAAC,CAAC,CAAC,IAAI;IAC/B,CAAC;EACH,CAAC;;EAED;EACA,MAAMH,sBAAsB,GAAII,gBAAgB,IAAK;IACnD,IAAI;MACF,IAAI,CAACA,gBAAgB,EAAE,OAAO,CAAC,CAAC;MAChC,OAAOX,IAAI,CAACC,KAAK,CAACU,gBAAgB,CAAC;IACrC,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACd,OAAO,CAAC,CAAC;IACX;EACF,CAAC;;EAED;EACA,MAAMiD,mBAAmB,GAAGA,CAACJ,UAAU,EAAEK,aAAa,KAAK;IACzD,MAAMP,gBAAgB,GAAGC,sBAAsB,CAACC,UAAU,CAAC;IAC3D,OAAOF,gBAAgB,CAACO,aAAa,CAAC,IAAI,EAAE;EAC9C,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAIC,MAAM,IAAK;IACzC9C,cAAc,CAAC;MACb+C,SAAS,EAAE;QAAED,MAAM;QAAEE,cAAc,EAAE;MAAE,CAAC;MACxCC,cAAc,EAAE,CAAC;QAAEC,KAAK,EAAElE;MAAe,CAAC;IAC5C,CAAC,CAAC,CACDmE,KAAK,CAAEzD,KAAK,IAAK;MAChBQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD0D,KAAK,CAAC,8CAA8C,CAAC;IACvD,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAACP,MAAM,EAAEQ,eAAe,KAAK;IAC1D,IAAIA,eAAe,GAAG,CAAC,EAAE;MACvB;MACAtD,cAAc,CAAC;QACb+C,SAAS,EAAE;UAAED,MAAM;UAAEE,cAAc,EAAE,CAAC;QAAE,CAAC;QACzCC,cAAc,EAAE,CAAC;UAAEC,KAAK,EAAElE;QAAe,CAAC;MAC5C,CAAC,CAAC,CACDmE,KAAK,CAAEzD,KAAK,IAAK;QAChBQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD0D,KAAK,CAAC,8CAA8C,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACApD,cAAc,CAAC;QACb+C,SAAS,EAAE;UAAED,MAAM;UAAEE,cAAc,EAAE,CAAC;QAAE,CAAC;QACzCC,cAAc,EAAE,CAAC;UAAEC,KAAK,EAAElE;QAAe,CAAC,CAAC;QAC3CuE,mBAAmB,EAAE;MACvB,CAAC,CAAC,CACDJ,KAAK,CAAEzD,KAAK,IAAK;QAChBQ,OAAO,CAACR,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE0D,KAAK,CAAC,0CAA0C,CAAC;MACnD,CAAC,CAAC;IACJ;EACF,CAAC;;EAID;EACA,MAAMI,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MAAA,IAAAC,YAAA,EAAAC,qBAAA;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,gDAAgD,EAAE;QAC7EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEhC,IAAI,CAACiC,SAAS,CAAC;UACnBd,KAAK,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;QACQ,CAAC;MACH,CAAC,CAAC;MAEF,MAAMe,MAAM,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAEpC,KAAAT,YAAA,GAAIQ,MAAM,CAACtE,IAAI,cAAA8D,YAAA,gBAAAC,qBAAA,GAAXD,YAAA,CAAaU,UAAU,cAAAT,qBAAA,eAAvBA,qBAAA,CAAyBU,OAAO,EAAE;QACpC;QACA,MAAMxE,OAAO,CAAC,CAAC;QACfwD,KAAK,CAAC,4BAA4B,CAAC;QACnC9D,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QAAA,IAAA+E,aAAA,EAAAC,qBAAA;QACL,MAAM,IAAIC,KAAK,CAAC,EAAAF,aAAA,GAAAJ,MAAM,CAACtE,IAAI,cAAA0E,aAAA,wBAAAC,qBAAA,GAAXD,aAAA,CAAaF,UAAU,cAAAG,qBAAA,uBAAvBA,qBAAA,CAAyB5D,OAAO,KAAI,cAAc,CAAC;MACrE;IAEF,CAAC,CAAC,OAAO8D,GAAG,EAAE;MACZtE,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAE8E,GAAG,CAAC;MACxCpB,KAAK,CAAC,0BAA0BoB,GAAG,CAAC9D,OAAO,EAAE,CAAC;IAChD;EACF,CAAC;EAED,oBACEvB,OAAA;IAAKiB,SAAS,EAAC,iBAAiB;IAACqE,OAAO,EAAEnF,UAAW;IAAAe,QAAA,eACnDlB,OAAA;MAAKiB,SAAS,EAAC,gBAAgB;MAACqE,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAtE,QAAA,gBAClElB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlB,OAAA;UAAAkB,QAAA,GAAI,UAAQ,EAACkB,iBAAiB,CAAC,CAAC,EAAC,QAAM;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5CtB,OAAA;UAAQiB,SAAS,EAAC,WAAW;UAACqE,OAAO,EAAEnF,UAAW;UAAAe,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,EACL,CAACE,eAAe,IAAIA,eAAe,CAACiE,MAAM,KAAK,CAAC,gBAC/CzF,OAAA;QAAAkB,QAAA,EAAG;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAE1BtB,OAAA;QAAKiB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BlB,OAAA;UAAKiB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAClCM,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkE,GAAG,CAAC,CAAC3D,IAAI,EAAE4D,KAAK,KAAK;YACrC,MAAMC,WAAW,GAAGnD,kBAAkB,CAACV,IAAI,CAAC8D,EAAE,EAAE9D,IAAI,CAACC,OAAO,CAACoB,UAAU,CAAC;;YAExE;YACA,MAAMC,cAAc,GAAGG,mBAAmB,CAACzB,IAAI,CAACC,OAAO,CAACoB,UAAU,EAAE,MAAM,CAAC,IACtDI,mBAAmB,CAACzB,IAAI,CAACC,OAAO,CAACoB,UAAU,EAAE,MAAM,CAAC,IACpD,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAC5C,MAAME,eAAe,GAAGE,mBAAmB,CAACzB,IAAI,CAACC,OAAO,CAACoB,UAAU,EAAE,OAAO,CAAC,IACtDI,mBAAmB,CAACzB,IAAI,CAACC,OAAO,CAACoB,UAAU,EAAE,OAAO,CAAC,IACrD,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;;YAI1D,oBACEpD,OAAA;cAAmBiB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtClB,OAAA;gBAAKiB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BlB,OAAA;kBAAIiB,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEa,IAAI,CAACC,OAAO,CAAC8D;gBAAI;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrDtB,OAAA;kBAAGiB,SAAS,EAAC,eAAe;kBAAC,eAAY,kBAAkB;kBAAAC,QAAA,GAAC,GAAC,EAACa,IAAI,CAACC,OAAO,CAACC,KAAK;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAEpF+B,cAAc,CAACoC,MAAM,GAAG,CAAC,iBACxBzF,OAAA;kBAAKiB,SAAS,EAAC,cAAc;kBAAC,eAAa,uBAAuBoB,WAAW,CAAC,MAAM,CAAC,EAAG;kBAAAnB,QAAA,gBACtFlB,OAAA;oBAAMiB,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzCtB,OAAA;oBAAKiB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC1BmC,cAAc,CAACqC,GAAG,CAAC5C,IAAI,iBACtB9C,OAAA;sBAEEiB,SAAS,EAAE,YAAY2E,WAAW,CAAC9C,IAAI,KAAKA,IAAI,GAAG,UAAU,GAAG,EAAE,gBAAiB;sBACnF,eAAa,uBAAuBT,WAAW,CAAC,MAAM,CAAC,IAAIA,WAAW,CAACS,IAAI,CAAC,GAAG8C,WAAW,CAAC9C,IAAI,KAAKA,IAAI,GAAG,WAAW,GAAG,EAAE,EAAG;sBAAA5B,QAAA,EAE7H4B;oBAAI,GAJAA,IAAI;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAKL,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAEAgC,eAAe,CAACmC,MAAM,GAAG,CAAC,iBACzBzF,OAAA;kBAAKiB,SAAS,EAAC,eAAe;kBAAC,eAAa,uBAAuBoB,WAAW,CAAC,OAAO,CAAC,EAAG;kBAAAnB,QAAA,gBACxFlB,OAAA;oBAAMiB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3CtB,OAAA;oBAAKiB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAC3BoC,eAAe,CAACoC,GAAG,CAAC1C,KAAK,iBACxBhD,OAAA;sBAEEiB,SAAS,EAAE,gBAAgB2E,WAAW,CAAC5C,KAAK,KAAKA,KAAK,GAAG,UAAU,GAAG,EAAE,gBAAiB;sBACzF+C,KAAK,EAAE;wBAACC,eAAe,EAAEhD;sBAAK,CAAE;sBAChC,eAAa,uBAAuBX,WAAW,CAAC,OAAO,CAAC,IAAIA,WAAW,CAACW,KAAK,CAAC,GAAG4C,WAAW,CAAC5C,KAAK,KAAKA,KAAK,GAAG,WAAW,GAAG,EAAE;oBAAG,GAH7HA,KAAK;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAIN,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAERtB,OAAA;gBAAKiB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BlB,OAAA;kBAAKiB,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BlB,OAAA;oBAAKiG,GAAG,EAAElE,IAAI,CAACC,OAAO,CAACkE,KAAM;oBAACC,GAAG,EAAEpE,IAAI,CAACC,OAAO,CAAC8D;kBAAK;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNtB,OAAA;kBAAKiB,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BlB,OAAA;oBAAKiB,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBACjClB,OAAA;sBACEiB,SAAS,EAAC,cAAc;sBACxB,eAAY,2BAA2B;sBACvCqE,OAAO,EAAEA,CAAA,KAAMpB,sBAAsB,CAACnC,IAAI,CAAC8D,EAAE,EAAE9D,IAAI,CAACG,QAAQ,CAAE;sBAAAhB,QAAA,EAC/D;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTtB,OAAA;sBAAMiB,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEa,IAAI,CAACG;oBAAQ;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzDtB,OAAA;sBACEiB,SAAS,EAAC,cAAc;sBACxB,eAAY,2BAA2B;sBACvCqE,OAAO,EAAEA,CAAA,KAAM5B,sBAAsB,CAAC3B,IAAI,CAAC8D,EAAE,CAAE;sBAAA3E,QAAA,EAChD;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNtB,OAAA;oBAAKiB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEyE,KAAK,GAAG;kBAAC;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA/DIS,IAAI,CAAC8D,EAAE;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgEd,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtB,OAAA;UAAKiB,SAAS,EAAC,eAAe;UAAC,eAAY,YAAY;UAAAC,QAAA,gBACrDlB,OAAA;YAAAkB,QAAA,EAAG;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACZtB,OAAA;YAAAkB,QAAA,GAAG,GAAC,EAACQ,cAAc,CAAC,CAAC;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACNtB,OAAA;UAAQsF,OAAO,EAAEjB,gBAAiB;UAACpD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjB,EAAA,CAtRQJ,SAAS;EAAA,QAC0BN,QAAQ,EAYzBC,WAAW;AAAA;AAAAwG,EAAA,GAb7BnG,SAAS;AAwRlB,eAAeA,SAAS;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}