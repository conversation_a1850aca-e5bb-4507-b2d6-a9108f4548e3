{"ast": null, "code": "import { gql } from '@apollo/client';\nexport const GET_PRODUCTS = gql`\n  query products {\n    products {\n      id\n      name\n      amount\n      inStock\n      image_url\n      category_id\n    }\n  }\n`;\nexport const GET_PRODUCT_DETAILS = gql`\n  query product($id: ID!) {\n    product(id: $id) {\n      id\n      name\n      brand\n      description\n      inStock\n      amount\n      image_url\n      attributes\n    }\n  }\n`;\nexport const GET_PRODUCT_ATTRIBUTES = gql`\n  query attributes($id: ID!) {\n    attributes(id: $id)\n  }\n`;\nexport const GET_CART_QUERY = gql`\n  query cart {\n    cart {\n      id\n      product {\n        id\n        name\n        price\n        image\n        attributes\n      }\n      quantity\n      selectedAttributes\n    }\n  }\n`;", "map": {"version": 3, "names": ["gql", "GET_PRODUCTS", "GET_PRODUCT_DETAILS", "GET_PRODUCT_ATTRIBUTES", "GET_CART_QUERY"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/graphql/queries.js"], "sourcesContent": ["import { gql } from '@apollo/client';\r\n\r\nexport const GET_PRODUCTS = gql`\r\n  query products {\r\n    products {\r\n      id\r\n      name\r\n      amount\r\n      inStock\r\n      image_url\r\n      category_id\r\n    }\r\n  }\r\n`;\r\n\r\nexport const GET_PRODUCT_DETAILS = gql`\r\n  query product($id: ID!) {\r\n    product(id: $id) {\r\n      id\r\n      name\r\n      brand\r\n      description\r\n      inStock\r\n      amount\r\n      image_url\r\n      attributes\r\n    }\r\n  }\r\n`;\r\n\r\nexport const GET_PRODUCT_ATTRIBUTES = gql`\r\n  query attributes($id: ID!) {\r\n    attributes(id: $id)\r\n  }\r\n`;\r\n\r\nexport const GET_CART_QUERY = gql`\r\n  query cart {\r\n    cart {\r\n      id\r\n      product {\r\n        id\r\n        name\r\n        price\r\n        image\r\n        attributes\r\n      }\r\n      quantity\r\n      selectedAttributes\r\n    }\r\n  }\r\n`;\r\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,OAAO,MAAMC,YAAY,GAAGD,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAME,mBAAmB,GAAGF,GAAG;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMG,sBAAsB,GAAGH,GAAG;AACzC;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMI,cAAc,GAAGJ,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}